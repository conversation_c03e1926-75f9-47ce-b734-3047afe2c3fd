.message-item {
  margin-bottom: 16px;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.message-item.user {
  justify-content: flex-end;
  flex-direction: row-reverse;
}

.message-item.assistant {
  justify-content: flex-start;
}

.message-item.tool {
  justify-content: flex-start;
}

.message-wrapper {
  display: flex;
  flex-direction: column;
  max-width: 85%;
}

.message-content {
  padding: 2px 16px;
  border-radius: 12px;
  word-wrap: break-word;
  line-height: 1.5;
  white-space: pre-wrap;
  text-align: left;
}

.message-item.user .message-content {
  background: #1890ff;
  color: #ffffff;
  border-bottom-right-radius: 4px;
}

.message-item.assistant .message-content {
  background: #f5f5f5;
  color: #333333;
  border-bottom-left-radius: 4px;
  text-align: left;
}

.assistant-bubble {
  background: #f5f5f5;
  color: #333333;
  border-bottom-left-radius: 4px;
  text-align: left;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.message-item.tool .message-content {
  background: #fff7e6;
  color: #d46b08;
  border: 1px solid #ffd591;
  border-bottom-left-radius: 4px;
}

.message-time {
  font-size: 12px;
  color: #999999;
  margin-top: 4px;
  text-align: center;
}

.tool-call {
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 8px;
  padding: 12px;
  margin: 8px 0;
  width: 100%;
}

.tool-call-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 500;
  color: #d46b08;
}

.tool-call-content {
  background: #ffffff;
  border-radius: 4px;
  padding: 8px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  overflow-x: auto;
}

.tool-call-content div {
  margin-bottom: 4px;
}

.tool-call-content div:last-child {
  margin-bottom: 0;
}

.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(5, end) infinite;
}

@keyframes dots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

.message-item.assistant .ant-bubble-content,
.message-item.assistant .ant-bubble-content-filled {
  text-align: left;
}

/* Markdown 样式 */
.message-content h1,
.message-content h2,
.message-content h3,
.message-content h4,
.message-content h5,
.message-content h6 {
  margin: 16px 0 8px 0;
  font-weight: 600;
  line-height: 1.25;
}

.message-content h1 {
  font-size: 1.5em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 8px;
}

.message-content h2 {
  font-size: 1.25em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 6px;
}

.message-content h3 {
  font-size: 1.1em;
}

.message-content p {
  margin: 8px 0;
  line-height: 1.6;
}

.message-content ul,
.message-content ol {
  margin: 0px 0;
  padding-left: 20px;
}

.message-content li {
  margin: 0px 0;
  line-height: 1.5;
}

/* 自定义列表样式，防止自动生成序号 */
.markdown-ordered-list,
.markdown-unordered-list {
  list-style: none !important;
  counter-reset: none;
  padding-left: 0;
  margin: 0px 0;
}

.markdown-list-item {
  position: relative;
  padding-left: 0;
  margin: 0px 0;
  list-style: none !important;
}

/* 移除所有默认的列表样式 */
.markdown-list-item::marker {
  content: none !important;
}

.markdown-list-item::before {
  content: none !important;
}

/* 只有当内容不是以数字开头时，才显示项目符号 */
.message-content .markdown-list-item.bullet {
  padding-left: 20px;
}

.message-content .markdown-list-item.bullet::before {
  content: "•" !important;
  position: absolute;
  left: 0;
  color: #666;
}

/* 对于真正的有序列表项（内容以数字开头），不添加任何前缀 */
.message-content .markdown-list-item.numbered {
  padding-left: 0;
}

.message-content .markdown-list-item.numbered::before {
  content: none !important;
}

/* 确保在所有情况下都不显示默认的列表标记 */
.message-content ol,
.message-content ul {
  list-style: none !important;
  list-style-type: none !important;
}

.message-content ol li,
.message-content ul li {
  list-style: none !important;
  list-style-type: none !important;
}

.message-content ol li::marker,
.message-content ul li::marker {
  content: none !important;
  display: none !important;
}

/* 强制移除所有可能的列表标记 */
.message-content ol::before,
.message-content ul::before,
.message-content li::before {
  content: none !important;
}

/* 特别针对有序列表的::marker伪元素 */
.message-content ol {
  counter-reset: none !important;
}

.message-content ol li {
  counter-increment: none !important;
}

.message-content ol li::marker {
  content: "" !important;
  width: 0 !important;
  display: none !important;
}

/* 覆盖任何可能的浏览器默认样式 */
.assistant-bubble ol,
.assistant-bubble ul {
  list-style: none !important;
  list-style-type: none !important;
}

.assistant-bubble ol li,
.assistant-bubble ul li {
  list-style: none !important;
  list-style-type: none !important;
}

.assistant-bubble ol li::marker,
.assistant-bubble ul li::marker {
  content: none !important;
  display: none !important;
}

.message-content blockquote {
  margin: 8px 0;
  padding: 8px 16px;
  border-left: 4px solid #dfe2e5;
  background-color: #f6f8fa;
  color: #6a737d;
}

.message-content code {
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
  font-size: 85%;
  margin: 0;
  padding: 0.2em 0.4em;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
}

.message-content pre {
  background-color: #f6f8fa;
  border-radius: 6px;
  font-size: 85%;
  line-height: 1.45;
  overflow: auto;
  padding: 16px;
  margin: 0px 0;
}

.message-content pre code {
  background-color: transparent;
  border: 0;
  display: inline;
  line-height: inherit;
  margin: 0;
  max-width: auto;
  overflow: visible;
  padding: 0;
  word-wrap: normal;
}

.message-content a {
  color: #0366d6;
  text-decoration: none;
}

.message-content a:hover {
  text-decoration: underline;
}

.message-content hr {
  background-color: #e1e4e8;
  border: 0;
  height: 1px;
  margin: 16px 0;
}

/* 表格样式 */
.markdown-table-wrapper {
  overflow-x: auto;
  margin: 8px 0;
}

.message-content table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
  margin: 8px 0;
}

.message-content table th,
.message-content table td {
  border: 1px solid #dfe2e5;
  padding: 6px 13px;
  text-align: left;
}

.message-content table th {
  background-color: #f6f8fa;
  font-weight: 600;
}

.message-content table tr:nth-child(2n) {
  background-color: #f6f8fa;
}

/* 代码高亮容器样式 */
.message-content .react-syntax-highlighter-line-number {
  color: #999 !important;
}

.message-content pre[class*="language-"] {
  margin: 8px 0;
  border-radius: 6px;
  overflow: auto;
}

/* 确保在assistant消息中的markdown样式正确 */
.assistant-bubble h1,
.assistant-bubble h2,
.assistant-bubble h3,
.assistant-bubble h4,
.assistant-bubble h5,
.assistant-bubble h6,
.assistant-bubble p,
.assistant-bubble ul,
.assistant-bubble ol,
.assistant-bubble blockquote,
.assistant-bubble pre,
.assistant-bubble table {
  color: inherit;
}

.assistant-bubble code {
  background-color: rgba(27, 31, 35, 0.1);
}

.assistant-bubble pre {
  background-color: rgba(27, 31, 35, 0.05);
}