.message-item {
  margin-bottom: 16px;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.message-item.user {
  justify-content: flex-end;
  flex-direction: row-reverse;
}

.message-item.assistant {
  justify-content: flex-start;
}

.message-item.tool {
  justify-content: flex-start;
}

.message-wrapper {
  display: flex;
  flex-direction: column;
  max-width: 85%;
}

.message-content {
  padding: 12px 16px;
  border-radius: 12px;
  word-wrap: break-word;
  line-height: 1.5;
  white-space: pre-wrap;
  text-align: left;
}

.message-item.user .message-content {
  background: #1890ff;
  color: #ffffff;
  border-bottom-right-radius: 4px;
}

.message-item.assistant .message-content {
  background: #f5f5f5;
  color: #333333;
  border-bottom-left-radius: 4px;
  text-align: left;
}

.assistant-bubble {
  background: #f5f5f5;
  color: #333333;
  border-bottom-left-radius: 4px;
  text-align: left;
  white-space: pre-wrap;
  word-wrap: break-word;
  line-height: 1.5;
}

.message-item.tool .message-content {
  background: #fff7e6;
  color: #d46b08;
  border: 1px solid #ffd591;
  border-bottom-left-radius: 4px;
}

.message-time {
  font-size: 12px;
  color: #999999;
  margin-top: 4px;
  text-align: center;
}

.tool-call {
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 8px;
  padding: 12px;
  margin: 8px 0;
  width: 100%;
}

.tool-call-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 500;
  color: #d46b08;
}

.tool-call-content {
  background: #ffffff;
  border-radius: 4px;
  padding: 8px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  overflow-x: auto;
}

.tool-call-content div {
  margin-bottom: 4px;
}

.tool-call-content div:last-child {
  margin-bottom: 0;
}

.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(5, end) infinite;
}

@keyframes dots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

.message-item.assistant .ant-bubble-content,
.message-item.assistant .ant-bubble-content-filled {
  text-align: left;
} 